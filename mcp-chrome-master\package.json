{"name": "mcp-chrome-bridge-monorepo", "version": "1.0.0", "private": true, "author": "hangye", "type": "module", "scripts": {"build:shared": "pnpm --filter chrome-mcp-shared build", "build:native": "pnpm --filter mcp-chrome-bridge build", "build:extension": "pnpm --filter chrome-mcp-server build", "build:wasm": "pnpm --filter @chrome-mcp/wasm-simd build && pnpm run copy:wasm", "build": "pnpm -r --filter='!@chrome-mcp/wasm-simd' build", "copy:wasm": "cp ./packages/wasm-simd/pkg/simd_math.js ./packages/wasm-simd/pkg/simd_math_bg.wasm ./app/chrome-extension/workers/", "dev:shared": "pnpm --filter chrome-mcp-shared dev", "dev:native": "pnpm --filter mcp-chrome-bridge dev", "dev:extension": "pnpm --filter chrome-mcp-server dev", "dev": "pnpm --filter chrome-mcp-shared build && pnpm -r --parallel dev", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "format": "pnpm -r format", "clean:dist": "pnpm -r exec rm -rf dist .turbo", "clean:modules": "pnpm -r exec rm -rf node_modules && rm -rf node_modules", "clean": "npm run clean:dist && npm run clean:modules", "typecheck": "pnpm -r exec tsc --noEmit", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.25.1", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.0.0", "globals": "^16.1.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "vue-eslint-parser": "^10.1.3"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "**/*.{json,md,yaml,html,css}": ["prettier --write"]}}