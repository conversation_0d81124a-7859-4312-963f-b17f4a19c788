{"name": "@chrome-mcp/wasm-simd", "version": "0.1.0", "description": "SIMD-optimized WebAssembly math functions for Chrome MCP", "main": "pkg/simd_math.js", "types": "pkg/simd_math.d.ts", "files": ["pkg/"], "scripts": {"build": "wasm-pack build --target web --out-dir pkg --release", "build:dev": "wasm-pack build --target web --out-dir pkg --dev", "clean": "rimraf pkg/", "test": "wasm-pack test --headless --firefox"}, "keywords": ["wasm", "simd", "webassembly", "math", "cosine-similarity", "vector-operations"], "author": "hangye", "license": "MIT", "devDependencies": {"rimraf": "^5.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-repo/chrome-mcp-server.git", "directory": "packages/wasm-simd"}}