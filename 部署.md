## Chrome MCP Server + Open WebUI 部署指南（Windows | 小白可用）

本文将手把手教你把 Chrome 浏览器插件“Chrome MCP Server（mcp‑chrome）”接入 Open WebUI，使其可以在聊天中自动打开网页、截图、点击等。

推荐方案（稳定）：使用 mcpo（MCP→OpenAPI 代理）把 mcp‑chrome 暴露为 OpenAPI 接口，然后在 Open WebUI 中作为“OpenAPI 工具服务器”添加。

---

### 1. 最终效果与组件关系
- Chrome MCP Server（浏览器扩展）：把你的 Chrome 能力通过 MCP 协议暴露出来
- mcpo（MCP→OpenAPI 代理）：把 MCP 工具转换为标准 OpenAPI/REST 接口（http://127.0.0.1:8000）
- Open WebUI：连接 OpenAPI 工具服务器，驱动模型调用这些浏览器工具

---

### 2. 环境要求
- Windows 10/11
- Chrome 浏览器（最新版）
- Python 3.11+（用于安装运行 mcpo）
- Node.js 18+ 与 npm（用于安装 mcp‑chrome‑bridge）
- Open WebUI v0.6.20+（你已用 pip 安装并运行）

检查命令（任意 PowerShell）：
- node -v / npm -v
- python -V

如缺少请前往官网安装 Node.js 与 Python 3.11。

---

### 3. 安装 Chrome MCP Server 扩展（重要）
1) 获取扩展打包：
- 在你下载的源码包里通常已有发布版压缩包：mcp-chrome-*/releases/chrome-extension/latest/chrome-mcp-server-lastest.zip
- 将该 ZIP 解压到一个文件夹（解压后应能看到 manifest.json）

2) 在 Chrome 加载未打包扩展：
- 打开 chrome://extensions
- 打开“开发者模式”
- 点击“加载已解压的扩展程序”，选择上一步“包含 manifest.json 的那一层目录”
- 右上角出现图标后，点开扩展，点击“连接/Connect”（看到“服务运行中（端口：12306）”即成功）

若提示“清单文件缺失或不可读取”：说明选错了目录，必须选择能直接看到 manifest.json 的那层目录。

---

### 4. 安装并注册 Native Bridge（mcp-chrome-bridge）
在 PowerShell 执行（建议管理员终端，但普通用户也可）：
- npm install -g mcp-chrome-bridge
- mcp-chrome-bridge register

成功后会在用户注册表中创建 com.chromemcp.nativehost 映射，扩展即可通过 Native Messaging 与本机通信。

---

### 5. 安装 mcpo（MCP→OpenAPI 代理）
建议在专用虚拟环境中安装（以 C:\\Users\\<USER>\\mcpo 为例，可按需更改用户名）：
1) 创建并激活 venv（PowerShell）：
- python311 -m venv C:\\Users\\<USER>\\mcpo
- C:\\Users\\<USER>\\mcpo\\Scripts\\Activate.ps1
- python -V 确认 3.11+

2) 安装 mcpo（用官方源，避免镜像缺包）：
- pip install -i https://pypi.org/simple mcpo

---

### 6. 一键启动脚本（已为你准备）
仓库根目录下已创建：
- start-mcpo.bat（双击运行）
- start-mcpo.ps1（被 bat 调用）

默认行为：
- 自动激活 venv：C:\\Users\\<USER>\\mcpo
- 启动 mcpo 于 http://127.0.0.1:8000
- 以 stdio 方式挂载 mcp‑chrome‑bridge：C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js

可选参数（命令行传入）：
- 指定鉴权：start-mcpo.bat -ApiKey my-secret（Open WebUI 侧需选 Bearer，填同密钥）
- 改端口/监听：start-mcpo.bat -Port 8001 或 -ListenHost 0.0.0.0

若你的用户名不是 AAA，请编辑 start-mcpo.ps1 中的 $VenvDir 与 $StdioScript。

---

### 7. 在 Open WebUI 中添加 OpenAPI 工具服务器
1) 保持 start-mcpo.bat 打开的窗口处于运行状态
2) 打开浏览器访问 http://127.0.0.1:8000/docs，确认能看到 Swagger 文档（或访问 /openapi.json）
3) 打开 Open WebUI → 设置 → 工具 → 添加连接：
- URL: http://127.0.0.1:8000
- 鉴权：
  - 若启动 mcpo 未设置 --api-key：选择“无/None”
  - 若设置了 --api-key：选择 Bearer，并填入你的密钥
- 保存

Docker 提示（若你未来改为 Docker 运行 Open WebUI）：
- URL 改为 http://host.docker.internal:8000 或使用宿主机局域网 IP

---

### 8. 验证一次端到端调用
- 确保 Chrome 扩展“服务运行中（端口：12306）”，打开任意正常网页标签
- Open WebUI 选择支持“工具/函数调用”的模型
- 在聊天输入：
  - 打开 https://www.baidu.com 并截图首页
- 预期：工具调用 chrome_navigate 与 chrome_screenshot，返回截图或成功提示

截图保存位置：
- 扩展使用 Chrome 下载 API 保存 PNG 到“Chrome 默认下载目录”（一般为 C:\\Users\\<USER>\\Downloads）
- 在 chrome://downloads 可查看并打开其所在文件夹
- 想要直接在聊天里返回图片，可提示让工具使用 storeBase64: true（不落盘，返回 base64）

---

### 9. 常见问题（FAQ）
1) Open WebUI 说 openapi.json 为空/无法访问：
- 确保 http://127.0.0.1:8000/openapi.json 能在浏览器打开
- start-mcpo 的窗口必须保持打开；端口一致
- 防火墙是否拦截；换端口试试（-Port 8001）

2) 加载扩展时报“清单缺失”：
- 你选择的不是包含 manifest.json 的那一层目录，请解压官方打包 ZIP 并选对目录

3) 截图不出来或报权限：
- Chrome 限制：chrome://、扩展商店等特殊页面无法截图
- 在扩展权限里允许“下载/截图/注入脚本”等（扩展默认已申请，需要你允许）

4) mcpo 启动失败：
- venv 未找到或未激活 → 检查 C:\\Users\\<USER>\\mcpo 路径
- mcpo 未安装 → pip install -i https://pypi.org/simple mcpo
- Node.js 未安装或不在 PATH → 安装 Node.js 再试
- mcp‑chrome‑bridge 路径不对 → 运行 npm root -g 查全局目录，修正 start-mcpo.ps1 中 $StdioScript

5) Open WebUI 在 Docker 里连不上：
- URL 用 http://host.docker.internal:8000 或宿主机 IP；或把容器设为 host 网络

---

### 10. 每次使用的最简流程
1) 打开 Chrome（扩展自动加载）
2) 双击 start-mcpo.bat（保持窗口打开）
3) 打开 Open WebUI → 开始聊天并使用工具

---

### 11. 安全建议
- 该方案允许模型操作你的浏览器，请关闭敏感页面，或用独立浏览器“个人资料/用户数据目录”以降低风险
- 不要对公网开放 mcpo（默认仅 127.0.0.1）

---

### 12. 常用命令速查
- 查看 npm 全局目录：npm root -g
- 查看 bridge 是否已装：npm -g ls mcp-chrome-bridge --depth=0
- 手工注册 bridge：mcp-chrome-bridge register
- 启动 mcpo（示例）：
  - mcpo --host 127.0.0.1 --port 8000 -- node "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"

---

祝使用愉快！如需把脚本路径或用户名换成你的实际值，直接编辑 start-mcpo.ps1 中的 $VenvDir 与 $StdioScript 即可。
