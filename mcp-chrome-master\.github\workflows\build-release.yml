# name: Build and Release Chrome Extension

# on:
#   push:
#     branches: [ master, develop ]
#     paths:
#       - 'app/chrome-extension/**'
#   pull_request:
#     branches: [ master ]
#     paths:
#       - 'app/chrome-extension/**'
#   workflow_dispatch:

# jobs:
#   build-extension:
#     runs-on: ubuntu-latest
    
#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v4
      
#     - name: Setup Node.js
#       uses: actions/setup-node@v4
#       with:
#         node-version: '18'
#         cache: 'npm'
#         cache-dependency-path: 'app/chrome-extension/package-lock.json'
        
#     - name: Install dependencies
#       run: |
#         cd app/chrome-extension
#         npm ci
        
#     - name: Build extension
#       run: |
#         cd app/chrome-extension
#         npm run build
        
#     - name: Create zip package
#       run: |
#         cd app/chrome-extension
#         npm run zip
        
#     - name: Prepare release directory
#       run: |
#         mkdir -p releases/chrome-extension/latest
#         mkdir -p releases/chrome-extension/$(date +%Y%m%d-%H%M%S)
        
#     - name: Copy release files
#       run: |
#         # Copy to latest
#         cp app/chrome-extension/.output/chrome-mv3-prod.zip releases/chrome-extension/latest/chrome-mcp-server-latest.zip
        
#         # Copy to timestamped version
#         TIMESTAMP=$(date +%Y%m%d-%H%M%S)
#         cp app/chrome-extension/.output/chrome-mv3-prod.zip releases/chrome-extension/$TIMESTAMP/chrome-mcp-server-$TIMESTAMP.zip
        
#     - name: Upload build artifacts
#       uses: actions/upload-artifact@v4
#       with:
#         name: chrome-extension-build
#         path: releases/chrome-extension/
#         retention-days: 30
        
#     - name: Commit and push releases (if on main branch)
#       if: github.ref == 'refs/heads/main'
#       run: |
#         git config --local user.email "<EMAIL>"
#         git config --local user.name "GitHub Action"
#         git add releases/
#         git diff --staged --quiet || git commit -m "Auto-build: Update Chrome extension release [skip ci]"
#         git push
